# Android登录应用程序

这是一个使用SharedPreferences存储用户名和密码的Android登录应用程序。

## 功能特性

1. **用户注册**: 新用户可以注册账户，用户名和密码会存储在SharedPreferences中
2. **用户登录**: 已注册用户可以使用用户名和密码登录
3. **数据持久化**: 使用SharedPreferences存储用户数据，应用重启后数据仍然存在
4. **存储位置查看**: 在主界面可以查看SharedPreferences文件的存储位置和内容
5. **安全退出**: 提供退出登录功能

## 技术实现

### SharedPreferences存储机制

- **存储文件名**: `UserPrefs.xml`
- **存储模式**: `Context.MODE_PRIVATE` (私有模式，只有本应用可以访问)
- **数据格式**: 
  - 用户密码: `{username}_password`
  - 注册时间: `{username}_register_time`

### 存储位置

SharedPreferences文件存储在以下位置：
```
/data/data/com.example.myapplication/shared_prefs/UserPrefs.xml
```

在Android设备上，具体路径为：
- **应用数据目录**: `/data/data/com.example.myapplication/`
- **SharedPrefs目录**: `/data/data/com.example.myapplication/shared_prefs/`
- **用户偏好文件**: `/data/data/com.example.myapplication/shared_prefs/UserPrefs.xml`

### 文件内容示例

```xml
<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
<map>
    <string name="admin_password">123456</string>
    <string name="admin_register_time">Mon Jan 01 10:30:00 GMT+08:00 2024</string>
    <string name="user1_password">password123</string>
    <string name="user1_register_time">Mon Jan 01 11:00:00 GMT+08:00 2024</string>
</map>
```

## 使用说明

1. **首次使用**: 点击"注册"按钮创建新账户
2. **登录**: 输入已注册的用户名和密码，点击"登录"
3. **查看存储信息**: 登录成功后，在主界面可以查看SharedPreferences的存储位置和内容
4. **退出**: 点击"退出登录"返回登录界面

## 安全注意事项

⚠️ **重要提醒**: 本应用仅用于学习和演示目的。在实际生产环境中：

1. **不要明文存储密码**: 应该使用加密或哈希算法处理密码
2. **使用更安全的存储方式**: 考虑使用Android Keystore或其他加密存储方案
3. **添加输入验证**: 实现更严格的用户名和密码验证规则
4. **网络安全**: 在实际应用中，认证应该通过安全的网络API进行

## 项目结构

```
app/src/main/
├── java/com/example/myapplication/
│   ├── MainActivity.java          # 登录界面
│   └── HomeActivity.java          # 主界面
├── res/
│   ├── layout/
│   │   ├── activity_main.xml      # 登录界面布局
│   │   └── activity_home.xml      # 主界面布局
│   └── values/
│       └── strings.xml            # 字符串资源
└── AndroidManifest.xml            # 应用配置文件
```

## 测试

项目包含单元测试，测试SharedPreferences的各种功能：

```bash
./gradlew test
```

测试内容包括：
- 用户注册功能
- 用户登录验证
- 多用户存储
- 数据持久性
- 输入验证

## 如何查看存储文件

### 方法1: 使用Android Studio Device File Explorer

1. 在Android Studio中打开Device File Explorer
2. 导航到 `/data/data/com.example.myapplication/shared_prefs/`
3. 找到 `UserPrefs.xml` 文件

### 方法2: 使用ADB命令

```bash
# 连接设备后执行
adb shell
su  # 需要root权限
cat /data/data/com.example.myapplication/shared_prefs/UserPrefs.xml
```

### 方法3: 应用内查看

应用的主界面会显示存储路径和当前存储的数据信息。

## 开发环境

- Android Studio
- Android SDK API 26+
- Java 11
- Gradle 8.9.2
