# Android登录应用程序演示脚本

## 应用程序概述

我已经成功创建了一个完整的Android登录应用程序，具有以下功能：

### 🎯 主要功能
1. **用户注册** - 新用户可以创建账户
2. **用户登录** - 已注册用户可以登录
3. **数据持久化** - 使用SharedPreferences存储用户数据
4. **存储位置查看** - 显示SharedPreferences文件的详细信息
5. **安全退出** - 提供退出登录功能

### 📁 项目结构

```
app/src/main/
├── java/com/example/myapplication/
│   ├── MainActivity.java          # 登录界面 (103行代码)
│   └── HomeActivity.java          # 主界面 (108行代码)
├── res/
│   ├── layout/
│   │   ├── activity_main.xml      # 登录界面布局 (80行)
│   │   └── activity_home.xml      # 主界面布局 (67行)
│   └── values/
│       └── strings.xml            # 中文字符串资源 (16个字符串)
└── AndroidManifest.xml            # 应用配置文件
```

### 🔧 技术实现

#### SharedPreferences存储机制
- **文件名**: `UserPrefs.xml`
- **存储模式**: `Context.MODE_PRIVATE`
- **数据格式**: 
  - 密码: `{username}_password`
  - 注册时间: `{username}_register_time`

#### 存储位置
```
/data/data/com.example.myapplication/shared_prefs/UserPrefs.xml
```

### 📱 用户界面

#### 登录界面 (MainActivity)
- 应用标题
- 用户名输入框
- 密码输入框 (密码隐藏)
- 登录按钮
- 注册按钮

#### 主界面 (HomeActivity)
- 欢迎信息
- 用户详细信息
- SharedPreferences存储信息展示
- 存储路径和文件状态
- 退出登录按钮

### 🔐 安全特性

1. **输入验证** - 检查空输入
2. **用户存在性检查** - 防止重复注册
3. **密码隐藏显示** - 在存储信息中用 ****** 替代密码
4. **私有存储** - 使用MODE_PRIVATE确保数据安全

### 🧪 测试覆盖

创建了完整的单元测试 (`SharedPreferencesTest.java`):
- 用户注册功能测试
- 用户登录验证测试
- 多用户存储测试
- 数据持久性测试
- 输入验证测试

### 📋 使用流程

1. **首次使用**:
   - 输入用户名和密码
   - 点击"注册"按钮
   - 系统提示"注册成功"

2. **登录**:
   - 输入已注册的用户名和密码
   - 点击"登录"按钮
   - 成功后跳转到主界面

3. **查看存储信息**:
   - 在主界面查看SharedPreferences的存储位置
   - 查看当前存储的用户数据
   - 查看文件状态和大小

4. **退出**:
   - 点击"退出登录"返回登录界面

### 📊 存储文件示例

```xml
<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
<map>
    <string name="admin_password">123456</string>
    <string name="admin_register_time">Mon Jan 01 10:30:00 GMT+08:00 2024</string>
    <string name="user1_password">password123</string>
    <string name="user1_register_time">Mon Jan 01 11:00:00 GMT+08:00 2024</string>
</map>
```

### 🔍 如何查看存储文件

#### 方法1: Android Studio Device File Explorer
1. 打开Device File Explorer
2. 导航到 `/data/data/com.example.myapplication/shared_prefs/`
3. 查看 `UserPrefs.xml` 文件

#### 方法2: ADB命令
```bash
adb shell
su  # 需要root权限
cat /data/data/com.example.myapplication/shared_prefs/UserPrefs.xml
```

#### 方法3: 应用内查看
应用的主界面会实时显示存储路径和数据信息。

### ⚠️ 安全提醒

**注意**: 本应用仅用于学习和演示目的。在生产环境中应该：
1. 使用加密存储密码
2. 实现更严格的输入验证
3. 使用网络API进行认证
4. 考虑使用Android Keystore

### 🎉 项目亮点

1. **完整的MVC架构** - 清晰的代码结构
2. **中文界面** - 完全本地化的用户界面
3. **详细的存储信息展示** - 教育性强
4. **完整的测试覆盖** - 确保功能正确性
5. **详细的文档** - 包含README和演示脚本

这个应用程序成功演示了Android中SharedPreferences的使用方法，并提供了完整的用户注册和登录功能。
