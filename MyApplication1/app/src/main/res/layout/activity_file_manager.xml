<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".FileManagerActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="SD卡文件管理"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- 文件名输入 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="文件名:"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/fileNameEditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="请输入文件名 (例如: note.txt)"
            android:textSize="16sp"
            android:padding="12dp"
            android:background="@drawable/edit_text_background"
            android:layout_marginBottom="16dp" />

        <!-- 文件内容输入 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="文件内容:"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/fileContentEditText"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:hint="请输入要保存的内容..."
            android:textSize="14sp"
            android:padding="12dp"
            android:gravity="top|start"
            android:inputType="textMultiLine"
            android:scrollbars="vertical"
            android:background="@drawable/edit_text_background"
            android:layout_marginBottom="24dp" />

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/writeFileButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="写入文件"
                android:textSize="14sp"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/readFileButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="读取文件"
                android:textSize="14sp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="24dp">

            <Button
                android:id="@+id/listFilesButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="列出文件"
                android:textSize="14sp"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/deleteFileButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="删除文件"
                android:textSize="14sp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- 文件信息显示 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="操作信息:"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/fileInfoTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="这里将显示文件操作的详细信息..."
            android:textSize="14sp"
            android:textColor="@color/black"
            android:background="#F0F0F0"
            android:padding="12dp"
            android:layout_marginBottom="24dp"
            android:minHeight="80dp" />

        <!-- 文件列表显示 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="文件列表:"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/fileListTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="点击'列出文件'按钮查看SD卡中的文件..."
            android:textSize="14sp"
            android:textColor="@color/black"
            android:background="#F8F8F8"
            android:padding="12dp"
            android:layout_marginBottom="24dp"
            android:minHeight="120dp" />

        <!-- 返回按钮 -->
        <Button
            android:id="@+id/backButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="返回主界面"
            android:textSize="16sp"
            android:padding="16dp" />

    </LinearLayout>

</ScrollView>
