package com.example.myapplication;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Environment;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class FileManagerActivity extends AppCompatActivity {

    private static final int PERMISSION_REQUEST_CODE = 1001;
    private EditText fileNameEditText;
    private EditText fileContentEditText;
    private TextView fileListTextView;
    private TextView fileInfoTextView;
    private Button writeFileButton;
    private Button readFileButton;
    private Button listFilesButton;
    private Button deleteFileButton;
    private Button backButton;

    private File sdCardDir;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_file_manager);

        initViews();
        checkPermissions();
        setupClickListeners();
        
        // 初始化SD卡目录
        initSDCardDirectory();
    }

    private void initViews() {
        fileNameEditText = findViewById(R.id.fileNameEditText);
        fileContentEditText = findViewById(R.id.fileContentEditText);
        fileListTextView = findViewById(R.id.fileListTextView);
        fileInfoTextView = findViewById(R.id.fileInfoTextView);
        writeFileButton = findViewById(R.id.writeFileButton);
        readFileButton = findViewById(R.id.readFileButton);
        listFilesButton = findViewById(R.id.listFilesButton);
        deleteFileButton = findViewById(R.id.deleteFileButton);
        backButton = findViewById(R.id.backButton);
    }

    private void setupClickListeners() {
        writeFileButton.setOnClickListener(v -> writeFileToSDCard());
        readFileButton.setOnClickListener(v -> readFileFromSDCard());
        listFilesButton.setOnClickListener(v -> listFilesInSDCard());
        deleteFileButton.setOnClickListener(v -> deleteFileFromSDCard());
        backButton.setOnClickListener(v -> finish());
    }

    private void checkPermissions() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED ||
            ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
            
            ActivityCompat.requestPermissions(this,
                    new String[]{
                            Manifest.permission.WRITE_EXTERNAL_STORAGE,
                            Manifest.permission.READ_EXTERNAL_STORAGE
                    },
                    PERMISSION_REQUEST_CODE);
        }
    }

    private void initSDCardDirectory() {
        // 获取外部存储目录（SD卡）
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            // 在应用专用目录下创建文件夹
            sdCardDir = new File(getExternalFilesDir(null), "MyAppFiles");
            if (!sdCardDir.exists()) {
                boolean created = sdCardDir.mkdirs();
                if (created) {
                    showInfo("SD卡目录创建成功: " + sdCardDir.getAbsolutePath());
                } else {
                    showInfo("SD卡目录创建失败");
                }
            } else {
                showInfo("SD卡目录: " + sdCardDir.getAbsolutePath());
            }
        } else {
            showInfo("SD卡不可用");
            Toast.makeText(this, "SD卡不可用", Toast.LENGTH_SHORT).show();
        }
    }

    private void writeFileToSDCard() {
        String fileName = fileNameEditText.getText().toString().trim();
        String content = fileContentEditText.getText().toString();

        if (fileName.isEmpty()) {
            Toast.makeText(this, "请输入文件名", Toast.LENGTH_SHORT).show();
            return;
        }

        if (sdCardDir == null) {
            Toast.makeText(this, "SD卡不可用", Toast.LENGTH_SHORT).show();
            return;
        }

        File file = new File(sdCardDir, fileName);
        FileOutputStream fos = null;

        try {
            fos = new FileOutputStream(file);
            fos.write(content.getBytes("UTF-8"));
            fos.flush();
            
            Toast.makeText(this, "文件写入成功: " + fileName, Toast.LENGTH_SHORT).show();
            showInfo("文件写入成功!\n路径: " + file.getAbsolutePath() + 
                    "\n大小: " + file.length() + " 字节");
            
            // 清空输入框
            fileContentEditText.setText("");
            
        } catch (IOException e) {
            Toast.makeText(this, "文件写入失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            showInfo("文件写入失败: " + e.getMessage());
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void readFileFromSDCard() {
        String fileName = fileNameEditText.getText().toString().trim();

        if (fileName.isEmpty()) {
            Toast.makeText(this, "请输入文件名", Toast.LENGTH_SHORT).show();
            return;
        }

        if (sdCardDir == null) {
            Toast.makeText(this, "SD卡不可用", Toast.LENGTH_SHORT).show();
            return;
        }

        File file = new File(sdCardDir, fileName);
        
        if (!file.exists()) {
            Toast.makeText(this, "文件不存在: " + fileName, Toast.LENGTH_SHORT).show();
            return;
        }

        FileInputStream fis = null;
        BufferedReader reader = null;

        try {
            fis = new FileInputStream(file);
            reader = new BufferedReader(new InputStreamReader(fis, "UTF-8"));
            
            StringBuilder content = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
            
            fileContentEditText.setText(content.toString());
            Toast.makeText(this, "文件读取成功: " + fileName, Toast.LENGTH_SHORT).show();
            
            // 显示文件信息
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            String fileInfo = "文件读取成功!\n" +
                    "文件名: " + file.getName() + "\n" +
                    "路径: " + file.getAbsolutePath() + "\n" +
                    "大小: " + file.length() + " 字节\n" +
                    "最后修改: " + sdf.format(new Date(file.lastModified()));
            showInfo(fileInfo);
            
        } catch (IOException e) {
            Toast.makeText(this, "文件读取失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            showInfo("文件读取失败: " + e.getMessage());
        } finally {
            try {
                if (reader != null) reader.close();
                if (fis != null) fis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private void listFilesInSDCard() {
        if (sdCardDir == null) {
            Toast.makeText(this, "SD卡不可用", Toast.LENGTH_SHORT).show();
            return;
        }

        File[] files = sdCardDir.listFiles();
        StringBuilder fileList = new StringBuilder();
        
        fileList.append("SD卡文件列表:\n");
        fileList.append("目录: ").append(sdCardDir.getAbsolutePath()).append("\n\n");

        if (files == null || files.length == 0) {
            fileList.append("目录为空");
        } else {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            
            for (File file : files) {
                if (file.isFile()) {
                    fileList.append("📄 ").append(file.getName()).append("\n");
                    fileList.append("   大小: ").append(file.length()).append(" 字节\n");
                    fileList.append("   修改时间: ").append(sdf.format(new Date(file.lastModified()))).append("\n\n");
                } else if (file.isDirectory()) {
                    fileList.append("📁 ").append(file.getName()).append("/\n\n");
                }
            }
        }

        fileListTextView.setText(fileList.toString());
        Toast.makeText(this, "文件列表已更新", Toast.LENGTH_SHORT).show();
    }

    private void deleteFileFromSDCard() {
        String fileName = fileNameEditText.getText().toString().trim();

        if (fileName.isEmpty()) {
            Toast.makeText(this, "请输入要删除的文件名", Toast.LENGTH_SHORT).show();
            return;
        }

        if (sdCardDir == null) {
            Toast.makeText(this, "SD卡不可用", Toast.LENGTH_SHORT).show();
            return;
        }

        File file = new File(sdCardDir, fileName);
        
        if (!file.exists()) {
            Toast.makeText(this, "文件不存在: " + fileName, Toast.LENGTH_SHORT).show();
            return;
        }

        if (file.delete()) {
            Toast.makeText(this, "文件删除成功: " + fileName, Toast.LENGTH_SHORT).show();
            showInfo("文件删除成功: " + fileName);
            fileContentEditText.setText("");
            // 自动刷新文件列表
            listFilesInSDCard();
        } else {
            Toast.makeText(this, "文件删除失败: " + fileName, Toast.LENGTH_SHORT).show();
            showInfo("文件删除失败: " + fileName);
        }
    }

    private void showInfo(String info) {
        fileInfoTextView.setText(info);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allPermissionsGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allPermissionsGranted = false;
                    break;
                }
            }
            
            if (allPermissionsGranted) {
                Toast.makeText(this, "权限已授予", Toast.LENGTH_SHORT).show();
                initSDCardDirectory();
            } else {
                Toast.makeText(this, "需要存储权限才能使用此功能", Toast.LENGTH_LONG).show();
            }
        }
    }
}
