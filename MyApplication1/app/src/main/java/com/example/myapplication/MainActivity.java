package com.example.myapplication;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import java.util.Date;

public class MainActivity extends AppCompatActivity {

    private EditText usernameEditText;
    private EditText passwordEditText;
    private Button loginButton;
    private Button registerButton;
    private Button directAccessButton;
    private SharedPreferences sharedPreferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 初始化视图
        initViews();

        // 获取SharedPreferences
        sharedPreferences = getSharedPreferences("UserPrefs", Context.MODE_PRIVATE);

        // 设置按钮点击事件
        setupClickListeners();
    }

    private void initViews() {
        usernameEditText = findViewById(R.id.usernameEditText);
        passwordEditText = findViewById(R.id.passwordEditText);
        loginButton = findViewById(R.id.loginButton);
        registerButton = findViewById(R.id.registerButton);
        directAccessButton = findViewById(R.id.directAccessButton);
    }

    private void setupClickListeners() {
        // 登录按钮
        loginButton.setOnClickListener(v -> login());

        // 注册按钮
        registerButton.setOnClickListener(v -> register());

        // 直接访问按钮（用于演示）
        directAccessButton.setOnClickListener(v -> directAccess());
    }

    private void login() {
        String username = usernameEditText.getText().toString().trim();
        String password = passwordEditText.getText().toString();

        if (username.isEmpty() || password.isEmpty()) {
            Toast.makeText(this, "请输入用户名和密码", Toast.LENGTH_SHORT).show();
            return;
        }

        // 检查用户是否存在
        String storedPassword = sharedPreferences.getString(username + "_password", null);

        if (storedPassword != null && storedPassword.equals(password)) {
            Toast.makeText(this, "登录成功", Toast.LENGTH_SHORT).show();

            // 跳转到主界面
            Intent intent = new Intent(this, HomeActivity.class);
            intent.putExtra("username", username);
            startActivity(intent);
            finish();
        } else {
            Toast.makeText(this, "用户名或密码错误", Toast.LENGTH_SHORT).show();
        }
    }

    private void register() {
        String username = usernameEditText.getText().toString().trim();
        String password = passwordEditText.getText().toString();

        if (username.isEmpty() || password.isEmpty()) {
            Toast.makeText(this, "请输入用户名和密码", Toast.LENGTH_SHORT).show();
            return;
        }

        // 检查用户是否已存在
        String existingPassword = sharedPreferences.getString(username + "_password", null);

        if (existingPassword != null) {
            Toast.makeText(this, "用户名已存在", Toast.LENGTH_SHORT).show();
            return;
        }

        // 注册新用户
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString(username + "_password", password);
        editor.putString(username + "_register_time", new Date().toString());
        editor.apply();

        Toast.makeText(this, "注册成功，请登录", Toast.LENGTH_SHORT).show();

        // 清空密码输入框
        passwordEditText.setText("");
    }

    private void directAccess() {
        // 直接访问文件管理（用于演示）
        Intent intent = new Intent(this, FileManagerActivity.class);
        startActivity(intent);
    }
}