package com.example.myapplication;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import java.io.File;

public class HomeActivity extends AppCompatActivity {

    private TextView welcomeTextView;
    private TextView userInfoTextView;
    private TextView storagePathTextView;
    private Button fileManagerButton;
    private Button logoutButton;
    private SharedPreferences sharedPreferences;
    private String currentUsername;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_home);

        // 初始化视图
        initViews();

        // 获取SharedPreferences
        sharedPreferences = getSharedPreferences("UserPrefs", Context.MODE_PRIVATE);

        // 获取当前用户名
        currentUsername = getIntent().getStringExtra("username");
        if (currentUsername == null) {
            currentUsername = "未知用户";
        }

        // 显示用户信息
        displayUserInfo();

        // 显示存储路径信息
        displayStorageInfo();

        // 设置按钮点击事件
        setupClickListeners();
    }

    private void initViews() {
        welcomeTextView = findViewById(R.id.welcomeTextView);
        userInfoTextView = findViewById(R.id.userInfoTextView);
        storagePathTextView = findViewById(R.id.storagePathTextView);
        fileManagerButton = findViewById(R.id.fileManagerButton);
        logoutButton = findViewById(R.id.logoutButton);
    }

    private void setupClickListeners() {
        // 文件管理按钮点击事件
        fileManagerButton.setOnClickListener(v -> openFileManager());
        
        // 退出登录按钮点击事件
        logoutButton.setOnClickListener(v -> logout());
    }

    private void displayUserInfo() {
        String welcomeMessage = getString(R.string.welcome) + ", " + currentUsername + "!";
        welcomeTextView.setText(welcomeMessage);

        // 显示用户的详细信息
        String userInfo = "当前登录用户: " + currentUsername + "\n" +
                "登录时间: " + new java.util.Date().toString();
        userInfoTextView.setText(userInfo);
    }

    private void displayStorageInfo() {
        try {
            // 获取应用的数据目录
            File dataDir = new File(getApplicationInfo().dataDir);
            File sharedPrefsDir = new File(dataDir, "shared_prefs");
            File prefsFile = new File(sharedPrefsDir, "UserPrefs.xml");

            StringBuilder storageInfo = new StringBuilder();
            storageInfo.append("SharedPreferences存储位置:\n");
            storageInfo.append("应用数据目录: ").append(dataDir.getAbsolutePath()).append("\n\n");
            storageInfo.append("SharedPrefs目录: ").append(sharedPrefsDir.getAbsolutePath()).append("\n\n");
            storageInfo.append("用户偏好文件: ").append(prefsFile.getAbsolutePath()).append("\n\n");

            // 检查文件是否存在
            if (prefsFile.exists()) {
                storageInfo.append("文件状态: 存在\n");
                storageInfo.append("文件大小: ").append(prefsFile.length()).append(" 字节\n");
            } else {
                storageInfo.append("文件状态: 不存在\n");
            }

            // 显示当前存储的用户数据
            storageInfo.append("\n当前存储的用户数据:\n");
            for (String key : sharedPreferences.getAll().keySet()) {
                if (key.endsWith("_password")) {
                    storageInfo.append(key).append(": ******\n");
                } else {
                    storageInfo.append(key).append(": ").append(sharedPreferences.getString(key, "")).append("\n");
                }
            }

            storagePathTextView.setText(storageInfo.toString());

        } catch (Exception e) {
            storagePathTextView.setText("获取存储信息时出错: " + e.getMessage());
        }
    }

    private void openFileManager() {
        Intent intent = new Intent(this, FileManagerActivity.class);
        startActivity(intent);
    }

    private void logout() {
        Toast.makeText(this, "已退出登录", Toast.LENGTH_SHORT).show();
        
        // 返回登录界面
        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent);
        finish();
    }
}
