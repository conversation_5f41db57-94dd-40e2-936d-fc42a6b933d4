package com.example.myapplication;

import android.content.Context;
import android.content.SharedPreferences;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * SharedPreferences功能的单元测试
 */
@RunWith(RobolectricTestRunner.class)
public class SharedPreferencesTest {

    private SharedPreferences sharedPreferences;
    private SharedPreferences.Editor editor;
    private Context context;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        context = RuntimeEnvironment.getApplication();
        sharedPreferences = context.getSharedPreferences("UserPrefs", Context.MODE_PRIVATE);
        editor = sharedPreferences.edit();
        
        // 清空之前的测试数据
        editor.clear().apply();
    }

    @Test
    public void testUserRegistration() {
        // 测试用户注册功能
        String username = "testuser";
        String password = "testpass123";
        
        // 模拟注册过程
        editor.putString(username + "_password", password);
        editor.putString(username + "_register_time", "2024-01-01");
        editor.apply();
        
        // 验证数据是否正确存储
        String storedPassword = sharedPreferences.getString(username + "_password", null);
        String registerTime = sharedPreferences.getString(username + "_register_time", null);
        
        assertNotNull("密码应该被存储", storedPassword);
        assertEquals("存储的密码应该匹配", password, storedPassword);
        assertNotNull("注册时间应该被存储", registerTime);
    }

    @Test
    public void testUserLogin() {
        // 先注册一个用户
        String username = "loginuser";
        String password = "loginpass123";
        
        editor.putString(username + "_password", password);
        editor.apply();
        
        // 测试正确的登录
        String inputPassword = "loginpass123";
        String storedPassword = sharedPreferences.getString(username + "_password", null);
        
        assertTrue("正确的密码应该能够登录", 
                storedPassword != null && storedPassword.equals(inputPassword));
        
        // 测试错误的登录
        String wrongPassword = "wrongpass";
        assertFalse("错误的密码不应该能够登录", 
                storedPassword != null && storedPassword.equals(wrongPassword));
    }

    @Test
    public void testUserNotExists() {
        // 测试不存在的用户
        String nonExistentUser = "nonexistent";
        String storedPassword = sharedPreferences.getString(nonExistentUser + "_password", null);
        
        assertNull("不存在的用户应该返回null", storedPassword);
    }

    @Test
    public void testMultipleUsers() {
        // 测试多个用户的存储
        String user1 = "user1";
        String pass1 = "pass1";
        String user2 = "user2";
        String pass2 = "pass2";
        
        // 注册两个用户
        editor.putString(user1 + "_password", pass1);
        editor.putString(user2 + "_password", pass2);
        editor.apply();
        
        // 验证两个用户都能正确存储和检索
        assertEquals("用户1密码应该正确", pass1, 
                sharedPreferences.getString(user1 + "_password", null));
        assertEquals("用户2密码应该正确", pass2, 
                sharedPreferences.getString(user2 + "_password", null));
    }

    @Test
    public void testDataPersistence() {
        // 测试数据持久性
        String username = "persistuser";
        String password = "persistpass";
        
        // 存储数据
        editor.putString(username + "_password", password);
        editor.apply();
        
        // 创建新的SharedPreferences实例模拟应用重启
        SharedPreferences newPrefs = context.getSharedPreferences("UserPrefs", Context.MODE_PRIVATE);
        String retrievedPassword = newPrefs.getString(username + "_password", null);
        
        assertEquals("数据应该在应用重启后仍然存在", password, retrievedPassword);
    }

    @Test
    public void testEmptyInputValidation() {
        // 测试空输入验证逻辑
        String emptyUsername = "";
        String emptyPassword = "";
        String nullUsername = null;
        String nullPassword = null;
        
        // 模拟输入验证逻辑
        assertTrue("空用户名应该被检测", isInputEmpty(emptyUsername));
        assertTrue("空密码应该被检测", isInputEmpty(emptyPassword));
        assertTrue("null用户名应该被检测", isInputEmpty(nullUsername));
        assertTrue("null密码应该被检测", isInputEmpty(nullPassword));
        
        String validInput = "validinput";
        assertFalse("有效输入不应该被标记为空", isInputEmpty(validInput));
    }
    
    // 辅助方法，模拟MainActivity中的输入验证逻辑
    private boolean isInputEmpty(String input) {
        return input == null || input.trim().isEmpty();
    }
}
